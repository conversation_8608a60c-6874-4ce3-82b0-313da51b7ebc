"""
应用配置模块
"""
import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本信息
    app_name: str = "Quantify Server"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 服务器配置
    host: str = "127.0.0.1"
    port: int = 8000
    
    # 日志配置
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# 全局配置实例
settings = Settings()
